import { gql } from "@apollo/client";
export const BRAND_OCCASIONS_QUERY = gql`
  query Occasions($gif: Boolean!) {
    occasions(gif: $gif) {
      edges {
        node {
          name
          nameEn
          code
          illustrationCount
          gifIllustrationCount
          isActive
        }
      }
    }
  }
`;

export const BRAND_ILLUSTRATIONS = gql`
  query Illustrations($code: String!, $first: Int, $after: String) {
    illustrations(occasionCode: $code, first: $first, after: $after) {
      edges {
        cursor
        node {
          occasion {
            name
            code
          }
          cardImage
          referenceCode
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

export const BRAND_GIF_ILLUSTRATION = gql`
  query GIFillustration($code: String!, $first: Int, $after: String) {
    gifIllustrations(occasionCode: $code, first: $first, after: $after) {
      edges {
        cursor
        node {
          occasion {
            name
            code
          }
          gifFile
          referenceCode
          gifImage
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;
